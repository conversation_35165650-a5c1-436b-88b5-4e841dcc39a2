#!/usr/bin/env python3
"""
SQLite Database Viewer GUI Application
Displays data from FlyrightDriveDatabase.db in a user-friendly interface
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import os

class DatabaseViewer:
    def __init__(self, root):
        self.root = root
        self.root.title("Flyright Drive Database Viewer")

        # Make fullscreen on launch
        try:
            # Try different methods for fullscreen
            self.root.state('zoomed')  # For Windows
        except:
            try:
                self.root.attributes('-zoomed', True)  # Alternative
            except:
                # Fallback: maximize window manually
                self.root.geometry(f"{self.root.winfo_screenwidth()}x{self.root.winfo_screenheight()}+0+0")

        # Database path
        self.db_path = "/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db"

        # Track current focus for arrow key navigation
        self.current_focus = "main"  # "main" or "log"
        self.current_log_tree = None  # Track the currently active log tree

        # Create menu bar
        self.create_menu()

        # Create main interface
        self.create_widgets()

        # Setup keyboard navigation
        self.setup_keyboard_navigation()

        # Load data on startup
        self.load_data()
    
    def create_menu(self):
        """Create the menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open Database...", command=self.open_database)
        file_menu.add_command(label="Refresh", command=self.load_data)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Show All Records", command=self.show_all_records)
        view_menu.add_command(label="Filter by Computer", command=self.filter_by_computer)
        view_menu.add_command(label="Filter by Simulator", command=self.filter_by_simulator)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_widgets(self):
        """Create the main interface widgets"""
        # Status frame
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(status_frame, text="Database:").pack(side=tk.LEFT)
        self.db_label = ttk.Label(status_frame, text=self.db_path, foreground="blue")
        self.db_label.pack(side=tk.LEFT, padx=(5, 0))

        # Search frame
        search_frame = ttk.Frame(self.root)
        search_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(5, 0))

        ttk.Button(search_frame, text="Clear", command=self.clear_search).pack(side=tk.LEFT, padx=(5, 0))

        # Record count label
        self.count_label = ttk.Label(search_frame, text="")
        self.count_label.pack(side=tk.RIGHT)

        # Main content frame with horizontal split
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create PanedWindow for resizable split
        self.paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True)

        # Left panel for main database list (reduced weight)
        left_panel = ttk.Frame(self.paned_window)
        self.paned_window.add(left_panel, weight=1)

        # Right panel for details - now with vertical split (increased weight)
        self.right_panel = ttk.Frame(self.paned_window)
        self.paned_window.add(self.right_panel, weight=3)

        # Create vertical PanedWindow for right panel split
        self.right_paned_window = ttk.PanedWindow(self.right_panel, orient=tk.VERTICAL)
        self.right_paned_window.pack(fill=tk.BOTH, expand=True)

        # Top right panel for tabs
        self.top_right_panel = ttk.Frame(self.right_paned_window)
        self.right_paned_window.add(self.top_right_panel, weight=2)

        # Bottom right panel for log details
        self.bottom_right_panel = ttk.Frame(self.right_paned_window)
        self.right_paned_window.add(self.bottom_right_panel, weight=1)

        # Treeview frame (now in left panel)
        tree_frame = ttk.Frame(left_panel)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create treeview with scrollbars
        self.tree = ttk.Treeview(tree_frame)
        
        # Vertical scrollbar
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        # Horizontal scrollbar
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Configure columns
        columns = ["Computer", "Simulator", "Asset Tag", "Serial Number", 
                  "Letter/PriSec", "Revision Number", "Sibling Drives", "Update Needed", "UUID"]
        
        self.tree["columns"] = columns
        self.tree["show"] = "headings"
        
        # Configure column headings and widths
        for col in columns:
            self.tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            self.tree.column(col, width=120, minwidth=80)
        
        # Bind selection event (single click/highlight)
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)

        # Bind focus events to track current focus
        self.tree.bind("<FocusIn>", lambda e: setattr(self, 'current_focus', 'main'))

        # Make sure the main tree can receive focus and keyboard events
        self.tree.configure(takefocus=True)

        # Bind keyboard navigation to main tree (will be set up after setup_keyboard_navigation is called)
        self.root.after(100, self.bind_main_tree_navigation)

        # Enable copy functionality for the main window
        self.setup_copy_functionality()

        # Initialize right panel with default content
        self.init_right_panel()

    def setup_keyboard_navigation(self):
        """Setup keyboard navigation between main and log viewers"""
        def switch_to_main():
            """Switch focus to main database viewer"""
            self.current_focus = "main"
            self.tree.focus_set()
            self.tree.focus_force()
            # Ensure main tree has a selection
            if not self.tree.selection():
                children = self.tree.get_children()
                if children:
                    self.tree.selection_set(children[0])
                    self.tree.focus(children[0])

        def switch_to_log():
            """Switch focus to log database viewer"""
            if self.current_log_tree:
                self.current_focus = "log"
                self.current_log_tree.focus_set()
                self.current_log_tree.focus_force()
                # Ensure log tree has a selection and is ready for keyboard input
                if not self.current_log_tree.selection():
                    children = self.current_log_tree.get_children()
                    if children:
                        # Select the last item (most recent log)
                        last_item = children[-1]
                        self.current_log_tree.selection_set(last_item)
                        self.current_log_tree.focus(last_item)
                        self.current_log_tree.see(last_item)

        def handle_main_tree_keys(event):
            if event.keysym == "Right":
                switch_to_log()
                return "break"
            return None

        def handle_log_tree_keys(event):
            if event.keysym == "Left":
                switch_to_main()
                return "break"
            return None

        # Store the navigation functions for later use
        self.switch_to_main = switch_to_main
        self.switch_to_log = switch_to_log
        self.handle_main_tree_keys = handle_main_tree_keys
        self.handle_log_tree_keys = handle_log_tree_keys

    def bind_main_tree_navigation(self):
        """Bind keyboard navigation to the main tree"""
        if hasattr(self, 'handle_main_tree_keys'):
            self.tree.bind("<KeyPress>", self.handle_main_tree_keys)

    def setup_copy_functionality(self):
        """Setup copy functionality for the application"""
        # Bind Ctrl+C to copy selected text
        self.root.bind('<Control-c>', self.copy_selected_text)
        self.root.bind('<Control-C>', self.copy_selected_text)

        # Also bind right-click context menu for copy
        self.root.bind('<Button-3>', self.show_context_menu)

    def copy_selected_text(self, event=None):
        """Copy selected text to clipboard"""
        try:
            # Get the widget that currently has focus
            focused_widget = self.root.focus_get()

            if focused_widget:
                # If it's an Entry widget, copy its selection
                if isinstance(focused_widget, tk.Entry):
                    if focused_widget.selection_present():
                        selected_text = focused_widget.selection_get()
                        self.root.clipboard_clear()
                        self.root.clipboard_append(selected_text)
                        return "break"

                # If it's a Text widget, copy its selection
                elif isinstance(focused_widget, tk.Text):
                    try:
                        selected_text = focused_widget.selection_get()
                        self.root.clipboard_clear()
                        self.root.clipboard_append(selected_text)
                        return "break"
                    except tk.TclError:
                        # No selection in text widget
                        pass

                # If it's the treeview, copy the selected row data
                elif focused_widget == self.tree:
                    selection = self.tree.selection()
                    if selection:
                        item = self.tree.item(selection[0])
                        values = item["values"]
                        # Format as tab-separated values
                        text_to_copy = "\t".join(str(val) for val in values)
                        self.root.clipboard_clear()
                        self.root.clipboard_append(text_to_copy)
                        return "break"
        except Exception:
            pass  # Silently ignore copy errors

        return None

    def show_context_menu(self, event):
        """Show right-click context menu with copy option"""
        try:
            # Create context menu
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label="Copy", command=self.copy_selected_text)

            # Show menu at cursor position
            context_menu.tk_popup(event.x_root, event.y_root)
        except Exception:
            pass  # Silently ignore menu errors
        finally:
            # Clean up menu
            try:
                context_menu.grab_release()
            except:
                pass

    def load_data(self):
        """Load data from the database"""
        try:
            if not os.path.exists(self.db_path):
                messagebox.showerror("Error", f"Database file not found: {self.db_path}")
                return
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM Drives ORDER BY Simulator, Computer, [Asset Tag]")
            self.all_data = cursor.fetchall()
            
            conn.close()
            
            self.display_data(self.all_data)
            
        except sqlite3.Error as e:
            messagebox.showerror("Database Error", f"Error reading database: {e}")
        except Exception as e:
            messagebox.showerror("Error", f"Unexpected error: {e}")
    
    def display_data(self, data):
        """Display data in the treeview"""
        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Insert new data
        for row in data:
            self.tree.insert("", tk.END, values=row)
        
        # Update record count
        self.count_label.config(text=f"Records: {len(data)}")
    
    def on_search(self, *args):
        """Handle search functionality"""
        search_term = self.search_var.get().lower()
        
        if not search_term:
            self.display_data(self.all_data)
            return
        
        # Filter data based on search term
        filtered_data = []
        for row in self.all_data:
            # Search in all columns
            if any(search_term in str(cell).lower() for cell in row):
                filtered_data.append(row)
        
        self.display_data(filtered_data)
    
    def clear_search(self):
        """Clear the search field"""
        self.search_var.set("")
    
    def sort_by_column(self, column):
        """Sort data by the specified column"""
        # Get column index
        columns = ["Computer", "Simulator", "Asset Tag", "Serial Number", 
                  "Letter/PriSec", "Revision Number", "Sibling Drives", "Update Needed", "UUID"]
        col_index = columns.index(column)
        
        # Get current data
        current_data = []
        for item in self.tree.get_children():
            current_data.append(self.tree.item(item)["values"])
        
        # Sort data
        try:
            # Try numeric sort for numeric columns
            if column in ["Simulator", "Revision Number", "UUID"]:
                current_data.sort(key=lambda x: int(x[col_index]) if x[col_index] else 0)
            else:
                current_data.sort(key=lambda x: str(x[col_index]).lower())
        except (ValueError, TypeError):
            # Fall back to string sort
            current_data.sort(key=lambda x: str(x[col_index]).lower())
        
        self.display_data(current_data)
    
    def on_item_select(self, event):
        """Handle selection of tree item"""
        try:
            selection = self.tree.selection()
            if selection:
                item = self.tree.item(selection[0])
                values = item["values"]
                self.show_record_details_in_panel(values)
        except Exception as e:
            messagebox.showerror("Error", f"Error opening record details: {e}")

    def init_right_panel(self):
        """Initialize the right panel with default content"""
        # Clear any existing content in top right panel
        for widget in self.top_right_panel.winfo_children():
            widget.destroy()

        # Clear any existing content in bottom right panel
        for widget in self.bottom_right_panel.winfo_children():
            widget.destroy()

        # Add title to top right panel
        title_label = ttk.Label(self.top_right_panel, text="Record Details",
                               font=("Arial", 12, "bold"))
        title_label.pack(pady=10)

        # Add instruction to top right panel
        instruction_label = ttk.Label(self.top_right_panel,
                                    text="Click on any record in the table\nto view detailed information and\nrelated simulator database records",
                                    justify=tk.CENTER, foreground="gray")
        instruction_label.pack(pady=20)

        # Add title to bottom right panel
        log_title_label = ttk.Label(self.bottom_right_panel, text="Log Details",
                                   font=("Arial", 12, "bold"))
        log_title_label.pack(pady=10)

        # Add instruction to bottom right panel
        log_instruction_label = ttk.Label(self.bottom_right_panel,
                                        text="Click on any log entry in the tables above\nto view detailed log information",
                                        justify=tk.CENTER, foreground="gray")
        log_instruction_label.pack(pady=20)

    def show_record_details_in_panel(self, values):
        """Show detailed view of a record in the right panel"""
        # Store current main record data for IMG name construction
        columns = ["Computer", "Simulator", "Asset Tag", "Serial Number",
                  "Letter/PriSec", "Revision Number", "Sibling Drives", "Update Needed", "UUID"]
        self.current_main_record = {}
        for i, col in enumerate(columns):
            if i < len(values):
                self.current_main_record[col] = values[i]

        # Clear existing content in top right panel
        for widget in self.top_right_panel.winfo_children():
            widget.destroy()

        # Initialize bottom panel
        self.init_log_details_panel()

        # Create scrollable frame for the content
        canvas = tk.Canvas(self.top_right_panel)
        scrollbar = ttk.Scrollbar(self.top_right_panel, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack scrollbar and canvas
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # Create notebook for tabs in the scrollable frame
        notebook = ttk.Notebook(scrollable_frame)
        notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Get computer and simulator values for related data
        computer = str(values[0]).replace(" ", "_")  # Replace spaces with underscores
        simulator = str(values[1])

        # Load related data from simulator databases FIRST (so they appear as first tabs)
        self.load_related_data_in_panel(notebook, computer, simulator)

        # Main record tab - ADD SECOND (so it appears after related data tabs)
        main_tab = ttk.Frame(notebook)
        notebook.add(main_tab, text="Main Record")

        columns = ["Computer", "Simulator", "Asset Tag", "Serial Number",
                  "Letter/PriSec", "Revision Number", "Sibling Drives", "Update Needed", "UUID"]

        # Create labels for each field in main tab
        for i, (col, val) in enumerate(zip(columns, values)):
            frame = ttk.Frame(main_tab)
            frame.pack(fill=tk.X, padx=5, pady=2)

            ttk.Label(frame, text=f"{col}:", font=("Arial", 9, "bold"), width=15).pack(side=tk.LEFT, anchor="w")

            # Use Entry widget for selectable/copyable text
            value_entry = tk.Entry(frame, font=("Arial", 9), relief="flat",
                                 readonlybackground="white", state="readonly",
                                 borderwidth=0, highlightthickness=0)
            value_entry.pack(side=tk.LEFT, padx=(10, 0), anchor="w", fill=tk.X, expand=True)
            value_entry.config(state="normal")
            value_entry.insert(0, str(val))
            value_entry.config(state="readonly")

        # Bind mousewheel to canvas for scrolling
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

    def init_log_details_panel(self):
        """Initialize the log details panel with default content"""
        # Clear any existing content in bottom right panel
        for widget in self.bottom_right_panel.winfo_children():
            widget.destroy()

        # Add title to bottom right panel
        log_title_label = ttk.Label(self.bottom_right_panel, text="Log Details",
                                   font=("Arial", 12, "bold"))
        log_title_label.pack(pady=10)

        # Add instruction to bottom right panel
        log_instruction_label = ttk.Label(self.bottom_right_panel,
                                        text="Click on any log entry in the tables above\nto view detailed log information",
                                        justify=tk.CENTER, foreground="gray")
        log_instruction_label.pack(pady=20)

    def show_log_details(self, log_data, column_names):
        """Show detailed view of a selected log entry"""
        # Clear existing content in bottom right panel
        for widget in self.bottom_right_panel.winfo_children():
            widget.destroy()

        # Add title
        title_label = ttk.Label(self.bottom_right_panel, text="Log Entry Details",
                               font=("Arial", 12, "bold"))
        title_label.pack(pady=10)

        # Create scrollable frame for log details
        canvas = tk.Canvas(self.bottom_right_panel)
        scrollbar = ttk.Scrollbar(self.bottom_right_panel, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack scrollbar and canvas
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # Add IMG Name section if this is a backup/restore entry
        img_name = self.construct_img_name(log_data, column_names)
        if img_name:
            # Add separator
            separator_frame = ttk.Frame(scrollable_frame)
            separator_frame.pack(fill=tk.X, padx=10, pady=(10, 5))
            ttk.Separator(separator_frame, orient='horizontal').pack(fill=tk.X)

            # Add IMG Name section title
            img_title_frame = ttk.Frame(scrollable_frame)
            img_title_frame.pack(fill=tk.X, padx=10, pady=5)
            ttk.Label(img_title_frame, text="IMG Name Information",
                     font=("Arial", 10, "bold"), foreground="blue").pack(side=tk.LEFT, anchor="w")

            # Add IMG Name field
            img_frame = ttk.Frame(scrollable_frame)
            img_frame.pack(fill=tk.X, padx=10, pady=2)

            ttk.Label(img_frame, text="IMG Name:", font=("Arial", 9, "bold"), width=15).pack(side=tk.LEFT, anchor="w")

            # Use Entry widget for selectable/copyable IMG name
            img_entry = tk.Entry(img_frame, font=("Arial", 9), relief="flat",
                               readonlybackground="#f0f8ff", state="readonly",
                               borderwidth=1, highlightthickness=1, width=60)
            img_entry.pack(side=tk.LEFT, padx=(10, 0), anchor="w", fill=tk.X, expand=True)
            img_entry.config(state="normal")
            img_entry.insert(0, img_name)
            img_entry.config(state="readonly")

            # Add another separator
            separator_frame2 = ttk.Frame(scrollable_frame)
            separator_frame2.pack(fill=tk.X, padx=10, pady=(5, 10))
            ttk.Separator(separator_frame2, orient='horizontal').pack(fill=tk.X)

        # Create labels for each field in the log entry
        for i, (col, val) in enumerate(zip(column_names, log_data)):
            frame = ttk.Frame(scrollable_frame)
            frame.pack(fill=tk.X, padx=10, pady=2)

            ttk.Label(frame, text=f"{col}:", font=("Arial", 9, "bold"), width=15).pack(side=tk.LEFT, anchor="w")

            # Use Entry widget for selectable/copyable text with longer width
            value_entry = tk.Entry(frame, font=("Arial", 9), relief="flat",
                                 readonlybackground="white", state="readonly",
                                 borderwidth=0, highlightthickness=0, width=60)
            value_entry.pack(side=tk.LEFT, padx=(10, 0), anchor="w", fill=tk.X, expand=True)
            value_entry.config(state="normal")
            value_entry.insert(0, str(val))
            value_entry.config(state="readonly")

    def construct_img_name(self, log_data, column_names):
        """Construct IMG name for backup/restore entries"""
        try:
            # Create a mapping from column names to values
            log_dict = {}
            for i, col_name in enumerate(column_names):
                if i < len(log_data):
                    log_dict[col_name] = log_data[i]

            # Check if this is a backup or restore entry by looking at the Action field or Log Comment
            action = str(log_dict.get('Action', '')).lower()
            log_comment = str(log_dict.get('Log Comment', '')).lower()

            # Look for backup/restore indicators in action or log comment
            is_backup_restore = (
                'backup' in action or 'restore' in action or
                'backup' in log_comment or 'restore' in log_comment or
                'img' in action or 'img' in log_comment
            )

            if not is_backup_restore:
                return None  # Not a backup/restore entry

            # Get the current main record data to extract simulator and computer
            if not hasattr(self, 'current_main_record'):
                return None

            simulator = str(self.current_main_record.get('Simulator', ''))
            computer = str(self.current_main_record.get('Computer', '')).replace(' ', '_')

            # Look for date field - try common variations
            current_date_database = ''
            date_fields = ['Date', 'date', 'Current Date', 'current_date_database', 'Database Date', 'Timestamp']
            for field in date_fields:
                if field in log_dict and log_dict[field]:
                    date_value = str(log_dict[field]).strip()
                    if date_value and date_value != 'None':
                        current_date_database = date_value
                        break

            # Look for revision number - try common variations
            new_rev_number = ''
            rev_fields = ['Revision Number', 'revision_number', 'new_rev_number', 'Rev Number', 'Rev', 'Version']
            for field in rev_fields:
                if field in log_dict and log_dict[field]:
                    rev_value = str(log_dict[field]).strip()
                    if rev_value and rev_value != 'None':
                        new_rev_number = rev_value
                        break

            # Only construct IMG name if we have all required components
            if simulator and computer and current_date_database and new_rev_number:
                # Clean up the components to ensure they're valid for filenames
                simulator = simulator.replace(' ', '_')
                computer = computer.replace(' ', '_')
                current_date_database = current_date_database.replace(' ', '_').replace(':', '-')
                new_rev_number = new_rev_number.replace(' ', '_')

                img_name = f"{simulator}-{computer}-{current_date_database}-{new_rev_number}-img"
                return img_name

            return None

        except Exception:
            return None

    def show_record_details(self, values):
        """Show detailed view of a record"""
        detail_window = tk.Toplevel(self.root)
        detail_window.title("Record Details")
        detail_window.geometry("400x300")
        detail_window.transient(self.root)

        # Center the window
        detail_window.update_idletasks()
        x = (detail_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (detail_window.winfo_screenheight() // 2) - (300 // 2)
        detail_window.geometry(f"400x300+{x}+{y}")

        # Make window visible first, then grab focus
        detail_window.deiconify()
        detail_window.lift()
        detail_window.focus_force()

        # Use after() to set grab after window is fully visible
        detail_window.after(100, lambda: self.set_window_grab(detail_window))

        columns = ["Computer", "Simulator", "Asset Tag", "Serial Number",
                  "Letter/PriSec", "Revision Number", "Sibling Drives", "Update Needed", "UUID"]

        # Create labels for each field
        for i, (col, val) in enumerate(zip(columns, values)):
            frame = ttk.Frame(detail_window)
            frame.pack(fill=tk.X, padx=10, pady=2)

            ttk.Label(frame, text=f"{col}:", font=("Arial", 9, "bold")).pack(side=tk.LEFT)

            # Use Entry widget for selectable/copyable text
            value_entry = tk.Entry(frame, font=("Arial", 9), relief="flat",
                                 readonlybackground="white", state="readonly",
                                 borderwidth=0, highlightthickness=0, width=30)
            value_entry.pack(side=tk.LEFT, padx=(10, 0))
            value_entry.config(state="normal")
            value_entry.insert(0, str(val))
            value_entry.config(state="readonly")

        # Close button
        ttk.Button(detail_window, text="Close", command=detail_window.destroy).pack(pady=10)

    def show_record_details_with_related(self, values):
        """Show detailed view of a record with related database records"""
        detail_window = tk.Toplevel(self.root)
        detail_window.title("Record Details with Related Data")
        detail_window.geometry("1000x700")
        detail_window.transient(self.root)

        # Center the window
        detail_window.update_idletasks()
        x = (detail_window.winfo_screenwidth() // 2) - (1800 // 2)
        y = (detail_window.winfo_screenheight() // 2) - (1200 // 2)
        detail_window.geometry(f"1800x1200+{x}+{y}")

        # Make window visible first, then grab focus
        detail_window.deiconify()
        detail_window.lift()
        detail_window.focus_force()

        # Use after() to set grab after window is fully visible
        detail_window.after(100, lambda: self.set_window_grab(detail_window))

        # Create notebook for tabs
        notebook = ttk.Notebook(detail_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Main record tab
        main_tab = ttk.Frame(notebook)
        notebook.add(main_tab, text="Main Record")

        columns = ["Computer", "Simulator", "Asset Tag", "Serial Number",
                  "Letter/PriSec", "Revision Number", "Sibling Drives", "Update Needed", "UUID"]

        # Create labels for each field in main tab
        for i, (col, val) in enumerate(zip(columns, values)):
            frame = ttk.Frame(main_tab)
            frame.pack(fill=tk.X, padx=10, pady=2)

            ttk.Label(frame, text=f"{col}:", font=("Arial", 9, "bold")).pack(side=tk.LEFT)

            # Use Entry widget for selectable/copyable text
            value_entry = tk.Entry(frame, font=("Arial", 9), relief="flat",
                                 readonlybackground="white", state="readonly",
                                 borderwidth=0, highlightthickness=0)
            value_entry.pack(side=tk.LEFT, padx=(10, 0), fill=tk.X, expand=True)
            value_entry.config(state="normal")
            value_entry.insert(0, str(val))
            value_entry.config(state="readonly")

        # Get computer and simulator values for related data
        computer = str(values[0]).replace(" ", "_")  # Replace spaces with underscores
        simulator = str(values[1])

        # Load related data from simulator databases
        self.load_related_data(notebook, computer, simulator)

        # Close button
        button_frame = ttk.Frame(detail_window)
        button_frame.pack(pady=10)
        ttk.Button(button_frame, text="Close", command=detail_window.destroy).pack()

    def set_window_grab(self, window):
        """Safely set window grab after window is visible"""
        try:
            if window.winfo_exists():
                window.grab_set()
        except tk.TclError:
            # If grab fails, just continue without it
            pass

    def reorder_log_columns(self, original_columns):
        """Reorder columns to put Log Comment second from left"""
        if "Log Comment" not in original_columns:
            return original_columns

        # Create new order with Log Comment second
        reordered = []

        # Add first column (usually Letter/Prisec)
        if original_columns:
            reordered.append(original_columns[0])

        # Add Log Comment as second column
        reordered.append("Log Comment")

        # Add remaining columns (except Log Comment which we already added)
        for col in original_columns[1:]:
            if col != "Log Comment":
                reordered.append(col)

        return reordered

    def reorder_row_data(self, row, original_columns, new_column_order):
        """Reorder row data to match the new column order"""
        if len(row) != len(original_columns):
            return row  # Return original if lengths don't match

        # Create a mapping from column name to value
        column_to_value = {}
        for i, col_name in enumerate(original_columns):
            column_to_value[col_name] = row[i] if i < len(row) else ""

        # Create new row in the desired order
        reordered_row = []
        for col_name in new_column_order:
            reordered_row.append(column_to_value.get(col_name, ""))

        return reordered_row

    def get_simulator_directory_name(self, simulator):
        """Map simulator values to their corresponding directory names"""
        simulator_str = str(simulator)

        # Handle special cases where simulator values don't match directory names
        simulator_mappings = {
            '16781697': '1678_1697',  # Map 16781697 to 1678_1697
            '393423': '393_423',      # Map 393423 to 393_423
        }

        # Check if we have a mapping for this simulator
        if simulator_str in simulator_mappings:
            return simulator_mappings[simulator_str]

        # For other simulators, use the value as-is
        return simulator_str

    def load_related_data_in_panel(self, notebook, computer, simulator):
        """Load related data from simulator databases for the right panel"""
        import glob

        # Handle special simulator name mappings
        simulator_dir = self.get_simulator_directory_name(simulator)

        # Base path for simulator databases
        base_path = f"/home/<USER>/Desktop/DriveDatabase/simulator_databases/{simulator_dir}"

        if not os.path.exists(base_path):
            # Create a tab to show no data found
            no_data_tab = ttk.Frame(notebook)
            notebook.add(no_data_tab, text="No Related Data")
            ttk.Label(no_data_tab, text=f"No simulator database found for simulator {simulator}").pack(pady=20)
            return

        # Find database files that match the computer name
        db_pattern = os.path.join(base_path, f"{computer}.db")
        db_files = glob.glob(db_pattern)

        # Also try with wildcards for partial matches
        if not db_files:
            db_pattern = os.path.join(base_path, f"*{computer}*.db")
            db_files = glob.glob(db_pattern)

        if not db_files:
            # Show all available databases for this simulator
            all_dbs = glob.glob(os.path.join(base_path, "*.db"))
            if all_dbs:
                self.show_all_simulator_databases_in_panel(notebook, all_dbs, simulator_dir)
            else:
                no_data_tab = ttk.Frame(notebook)
                notebook.add(no_data_tab, text="No Related Data")
                ttk.Label(no_data_tab, text=f"No databases found for simulator {simulator}").pack(pady=20)
            return

        # Load data from each matching database
        for db_file in db_files:
            db_name = os.path.basename(db_file).replace('.db', '')
            self.create_related_data_tab_in_panel(notebook, db_file, db_name)

    def show_all_simulator_databases_in_panel(self, notebook, db_files, simulator):
        """Show all available databases for a simulator when no exact match is found"""
        for db_file in db_files:
            db_name = os.path.basename(db_file).replace('.db', '')
            self.create_related_data_tab_in_panel(notebook, db_file, f"{db_name} (Sim {simulator})")

    def create_related_data_tab_in_panel(self, notebook, db_file, tab_name):
        """Create a tab showing data from a related database in the right panel"""
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            # Get table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            if not tables:
                conn.close()
                return

            # For now, assume the main table is 'logs' or use the first table
            table_name = 'logs'
            if ('logs',) not in tables:
                table_name = tables[0][0]

            # Get all data from the table
            cursor.execute(f"SELECT * FROM {table_name}")
            data = cursor.fetchall()

            # Get column names
            cursor.execute(f"PRAGMA table_info({table_name})")
            column_info = cursor.fetchall()
            original_column_names = [col[1] for col in column_info]

            # Reorder columns to put "Log Comment" second from left
            column_names = self.reorder_log_columns(original_column_names)

            conn.close()

            # Create tab
            tab = ttk.Frame(notebook)
            notebook.add(tab, text=tab_name)

            # Create treeview for the data (smaller for panel view)
            tree_frame = ttk.Frame(tab)
            tree_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

            tree = ttk.Treeview(tree_frame, height=8)  # Limit height for panel
            tree["columns"] = column_names
            tree["show"] = "headings"

            # Configure columns (smaller widths for panel)
            for col in column_names:
                tree.heading(col, text=col)
                # Make Log Comment column wider since it contains more text
                if col == "Log Comment":
                    tree.column(col, width=250, minwidth=100)
                else:
                    tree.column(col, width=80, minwidth=60)

            # Add scrollbars
            v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
            v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            tree.configure(yscrollcommand=v_scrollbar.set)

            h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=tree.xview)
            h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
            tree.configure(xscrollcommand=h_scrollbar.set)

            tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            # Insert data with reordered columns
            for row in data:
                reordered_row = self.reorder_row_data(row, original_column_names, column_names)
                tree.insert("", tk.END, values=reordered_row)

            # Set this as the current log tree for keyboard navigation
            self.current_log_tree = tree

            # Bind selection event to show log details
            def on_log_select(event, tree_widget=tree, cols=column_names):
                selection = tree_widget.selection()
                if selection:
                    item = tree_widget.item(selection[0])
                    values = item["values"]
                    self.show_log_details(values, cols)

            tree.bind("<<TreeviewSelect>>", on_log_select)

            # Bind focus events to track current focus
            tree.bind("<FocusIn>", lambda e: setattr(self, 'current_focus', 'log'))

            # Bind keyboard navigation for this log tree
            if hasattr(self, 'handle_log_tree_keys'):
                tree.bind("<KeyPress>", self.handle_log_tree_keys)

            # Make sure the tree can receive focus and keyboard events
            tree.configure(takefocus=True)

            # Auto-select the last log entry if data exists
            if data:
                children = tree.get_children()
                if children:
                    last_item = children[-1]
                    tree.selection_set(last_item)
                    tree.focus(last_item)
                    tree.see(last_item)
                    # Trigger the selection event to show details
                    item = tree.item(last_item)
                    values = item["values"]
                    self.show_log_details(values, column_names)

            # Add record count label
            count_label = ttk.Label(tab, text=f"Records: {len(data)}")
            count_label.pack(pady=2)

        except sqlite3.Error as e:
            # Create error tab
            tab = ttk.Frame(notebook)
            notebook.add(tab, text=f"Error: {tab_name}")
            ttk.Label(tab, text=f"Error loading database: {e}").pack(pady=20)
        except Exception as e:
            # Create error tab
            tab = ttk.Frame(notebook)
            notebook.add(tab, text=f"Error: {tab_name}")
            ttk.Label(tab, text=f"Unexpected error: {e}").pack(pady=20)

    def load_related_data(self, notebook, computer, simulator):
        """Load related data from simulator databases"""
        import glob

        # Handle special simulator name mappings
        simulator_dir = self.get_simulator_directory_name(simulator)

        # Base path for simulator databases
        base_path = f"/home/<USER>/Desktop/DriveDatabase/simulator_databases/{simulator_dir}"

        if not os.path.exists(base_path):
            # Create a tab to show no data found with debug info
            no_data_tab = ttk.Frame(notebook)
            notebook.add(no_data_tab, text="No Related Data")
            debug_text = f"No simulator database found for simulator '{simulator}'\n\nDebug Info:\n"
            debug_text += f"Computer: '{computer}'\n"
            debug_text += f"Simulator: '{simulator}' (type: {type(simulator)})\n"
            debug_text += f"Base path: {base_path}\n"
            debug_text += f"Path exists: {os.path.exists(base_path)}"
            # Use Text widget for selectable debug info
            debug_text_widget = tk.Text(no_data_tab, height=8, width=50, wrap=tk.WORD,
                                      font=("Courier", 9), relief="flat",
                                      borderwidth=0, highlightthickness=0)
            debug_text_widget.pack(pady=20, padx=20, fill=tk.BOTH, expand=True)
            debug_text_widget.insert("1.0", debug_text)
            debug_text_widget.config(state="disabled")  # Make read-only but still selectable
            return

        # Find database files that match the computer name
        db_pattern = os.path.join(base_path, f"{computer}.db")
        db_files = glob.glob(db_pattern)

        # Also try with wildcards for partial matches
        if not db_files:
            db_pattern = os.path.join(base_path, f"*{computer}*.db")
            db_files = glob.glob(db_pattern)

        if not db_files:
            # Show all available databases for this simulator
            all_dbs = glob.glob(os.path.join(base_path, "*.db"))
            if all_dbs:
                self.show_all_simulator_databases(notebook, all_dbs, simulator)
            else:
                no_data_tab = ttk.Frame(notebook)
                notebook.add(no_data_tab, text="No Related Data")
                ttk.Label(no_data_tab, text=f"No databases found for simulator {simulator}").pack(pady=20)
            return

        # Load data from each matching database
        for db_file in db_files:
            db_name = os.path.basename(db_file).replace('.db', '')
            self.create_related_data_tab(notebook, db_file, db_name)

    def show_all_simulator_databases(self, notebook, db_files, simulator):
        """Show all available databases for a simulator when no exact match is found"""
        for db_file in db_files:
            db_name = os.path.basename(db_file).replace('.db', '')
            self.create_related_data_tab(notebook, db_file, f"{db_name} (Sim {simulator})")

    def create_related_data_tab(self, notebook, db_file, tab_name):
        """Create a tab showing data from a related database"""
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            # Get table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            if not tables:
                conn.close()
                return

            # For now, assume the main table is 'logs' or use the first table
            table_name = 'logs'
            if ('logs',) not in tables:
                table_name = tables[0][0]

            # Get all data from the table
            cursor.execute(f"SELECT * FROM {table_name}")
            data = cursor.fetchall()

            # Get column names
            cursor.execute(f"PRAGMA table_info({table_name})")
            column_info = cursor.fetchall()
            original_column_names = [col[1] for col in column_info]

            # Reorder columns to put "Log Comment" second from left
            column_names = self.reorder_log_columns(original_column_names)

            conn.close()

            # Create tab
            tab = ttk.Frame(notebook)
            notebook.add(tab, text=tab_name)

            # Create treeview for the data
            tree_frame = ttk.Frame(tab)
            tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            tree = ttk.Treeview(tree_frame)
            tree["columns"] = column_names
            tree["show"] = "headings"

            # Configure columns
            for col in column_names:
                tree.heading(col, text=col)
                # Make Log Comment column wider since it contains more text
                if col == "Log Comment":
                    tree.column(col, width=200, minwidth=150)
                else:
                    tree.column(col, width=120, minwidth=80)

            # Add scrollbars
            v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
            v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            tree.configure(yscrollcommand=v_scrollbar.set)

            h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=tree.xview)
            h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
            tree.configure(xscrollcommand=h_scrollbar.set)

            tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            # Insert data with reordered columns
            for row in data:
                reordered_row = self.reorder_row_data(row, original_column_names, column_names)
                tree.insert("", tk.END, values=reordered_row)

            # Add record count label
            count_label = ttk.Label(tab, text=f"Records: {len(data)}")
            count_label.pack(pady=5)

        except sqlite3.Error as e:
            # Create error tab
            tab = ttk.Frame(notebook)
            notebook.add(tab, text=f"Error: {tab_name}")
            ttk.Label(tab, text=f"Error loading database: {e}").pack(pady=20)
        except Exception as e:
            # Create error tab
            tab = ttk.Frame(notebook)
            notebook.add(tab, text=f"Error: {tab_name}")
            ttk.Label(tab, text=f"Unexpected error: {e}").pack(pady=20)
    
    def open_database(self):
        """Open a different database file"""
        filename = filedialog.askopenfilename(
            title="Select Database File",
            filetypes=[("SQLite files", "*.db"), ("All files", "*.*")]
        )
        
        if filename:
            self.db_path = filename
            self.db_label.config(text=self.db_path)
            self.load_data()
    
    def show_all_records(self):
        """Show all records"""
        self.clear_search()
        self.display_data(self.all_data)
    
    def filter_by_computer(self):
        """Filter records by computer type"""
        # Get unique computer types
        computers = list(set(row[0] for row in self.all_data))
        computers.sort()
        
        # Create selection dialog
        self.create_filter_dialog("Filter by Computer", computers, 0)
    
    def filter_by_simulator(self):
        """Filter records by simulator number"""
        # Get unique simulator numbers
        simulators = list(set(str(row[1]) for row in self.all_data))
        simulators.sort()
        
        # Create selection dialog
        self.create_filter_dialog("Filter by Simulator", simulators, 1)
    
    def create_filter_dialog(self, title, options, column_index):
        """Create a filter selection dialog"""
        filter_window = tk.Toplevel(self.root)
        filter_window.title(title)
        filter_window.geometry("300x400")
        filter_window.transient(self.root)
        filter_window.grab_set()
        
        ttk.Label(filter_window, text="Select value to filter by:").pack(pady=10)
        
        # Listbox with options
        listbox = tk.Listbox(filter_window)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        for option in options:
            listbox.insert(tk.END, option)
        
        def apply_filter():
            selection = listbox.curselection()
            if selection:
                selected_value = listbox.get(selection[0])
                filtered_data = [row for row in self.all_data if str(row[column_index]) == selected_value]
                self.display_data(filtered_data)
                filter_window.destroy()
        
        # Buttons
        button_frame = ttk.Frame(filter_window)
        button_frame.pack(pady=10)
        
        ttk.Button(button_frame, text="Apply", command=apply_filter).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=filter_window.destroy).pack(side=tk.LEFT, padx=5)
    
    def show_about(self):
        """Show about dialog"""
        messagebox.showinfo("About", 
                          "Flyright Drive Database Viewer\n\n"
                          "A GUI application for viewing SQLite database records.\n"
                          "Double-click on any record for detailed view.\n\n"
                          "Features:\n"
                          "• Search across all fields\n"
                          "• Sort by column headers\n"
                          "• Filter by computer or simulator\n"
                          "• Detailed record view")

def main():
    root = tk.Tk()
    app = DatabaseViewer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
