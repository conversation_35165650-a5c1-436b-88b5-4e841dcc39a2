#!/usr/bin/env python3
"""
Test script to verify the split-pane layout works correctly
"""

import sys
import os
import sqlite3

def test_split_pane_functionality():
    """Test that the split-pane layout functionality works"""
    print("Testing split-pane layout functionality...\n")
    
    # Import our application
    sys.path.append('.')
    try:
        from db_viewer import DatabaseViewer
        import tkinter as tk
        from tkinter import ttk
        print("✓ Application imports successfully")
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False
    
    # Create test instance
    root = tk.Tk()
    root.withdraw()  # Hide window for test
    
    try:
        app = DatabaseViewer(root)
        
        # Test that split-pane components exist
        if hasattr(app, 'paned_window') and isinstance(app.paned_window, ttk.PanedWindow):
            print("✓ PanedWindow created successfully")
        else:
            print("✗ PanedWindow not found or wrong type")
            return False
        
        if hasattr(app, 'right_panel') and isinstance(app.right_panel, ttk.Frame):
            print("✓ Right panel created successfully")
        else:
            print("✗ Right panel not found or wrong type")
            return False
        
        # Test right panel initialization
        app.init_right_panel()
        children = app.right_panel.winfo_children()
        if len(children) > 0:
            print("✓ Right panel initialization creates content")
        else:
            print("✗ Right panel initialization failed")
            return False
        
        # Test the new methods exist
        if hasattr(app, 'show_record_details_in_panel'):
            print("✓ show_record_details_in_panel method exists")
        else:
            print("✗ show_record_details_in_panel method missing")
            return False
        
        if hasattr(app, 'load_related_data_in_panel'):
            print("✓ load_related_data_in_panel method exists")
        else:
            print("✗ load_related_data_in_panel method missing")
            return False
        
        # Test with sample data (simulate double-click)
        sample_values = ('Avionics Software', '1477', 'AT-002647', '1203032A3411', 'Primary', '3', '2', '', '1')
        
        print("\nTesting with sample record data...")
        
        # This would normally be called by double-click
        try:
            app.show_record_details_in_panel(sample_values)
            print("✓ Record details display in panel works")
            
            # Check that content was created
            children_after = app.right_panel.winfo_children()
            if len(children_after) > 0:
                print("✓ Panel content updated successfully")
            else:
                print("✗ Panel content not updated")
                return False
                
        except Exception as e:
            print(f"✗ Error showing record details: {e}")
            return False
        
        # Test database connection still works
        db_path = "/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db"
        if os.path.exists(db_path):
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM Drives")
                count = cursor.fetchone()[0]
                conn.close()
                print(f"✓ Database connection works: {count} records")
            except Exception as e:
                print(f"✗ Database connection failed: {e}")
                return False
        else:
            print("⚠ Main database not found (expected for some test environments)")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        root.destroy()
        return False

def main():
    print("="*60)
    print("Testing Split-Pane Layout Implementation")
    print("="*60)
    
    success = test_split_pane_functionality()
    
    print("\n" + "="*60)
    if success:
        print("✅ ALL TESTS PASSED!")
        print("\nThe split-pane layout is working correctly!")
        print("\nNew Features:")
        print("• Left panel: Main database table with search/filter")
        print("• Right panel: Detailed record view with related data")
        print("• Resizable panels: Drag the divider to adjust sizes")
        print("• Integrated workflow: No more popup windows")
        print("• Scrollable content: Handle large datasets in the right panel")
        print("• Log Comment positioning: Second column from left")
        print("\nUsage:")
        print("1. Run: python3 db_viewer.py")
        print("2. Double-click any record in the left table")
        print("3. View details and related data in the right panel")
        print("4. Drag the panel divider to resize as needed")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the errors above.")
    print("="*60)

if __name__ == "__main__":
    main()
