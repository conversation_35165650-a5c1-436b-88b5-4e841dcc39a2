#!/usr/bin/env python3
"""
Test script to verify the grab_set fix works
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_window_grab_fix():
    """Test that the window grab fix works correctly"""
    print("Testing window grab fix...")
    
    # Import our application
    sys.path.append('.')
    try:
        from db_viewer import DatabaseViewer
        print("✓ Application imports successfully")
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False
    
    # Test window creation without errors
    try:
        root = tk.Tk()
        root.withdraw()  # Hide main window
        
        # Create a test instance
        app = DatabaseViewer(root)
        
        # Test the set_window_grab method
        test_window = tk.Toplevel(root)
        test_window.title("Test")
        test_window.geometry("200x100")
        
        # Test the grab setting method
        app.set_window_grab(test_window)
        print("✓ Window grab method works without errors")
        
        # Clean up
        test_window.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ Window creation test failed: {e}")
        return False

def test_database_connection():
    """Test that database connection still works"""
    print("Testing database connection...")
    
    import sqlite3
    db_path = "/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db"
    
    try:
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM Drives")
            count = cursor.fetchone()[0]
            conn.close()
            print(f"✓ Database accessible: {count} records")
            return True
        else:
            print("✗ Database file not found")
            return False
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False

def main():
    print("Running tests for the grab_set fix...\n")
    
    test1 = test_database_connection()
    test2 = test_window_grab_fix()
    
    print("\n" + "="*50)
    if test1 and test2:
        print("✓ All tests passed! The fix should work correctly.")
        print("\nYou can now run the application with:")
        print("python3 db_viewer.py")
        print("\nDouble-clicking on rows should work without errors.")
    else:
        print("✗ Some tests failed. Please check the errors above.")
    print("="*50)

if __name__ == "__main__":
    main()
