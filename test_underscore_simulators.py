#!/usr/bin/env python3
"""
Test script to verify that simulators with underscores work correctly
"""

import sys
import os
import sqlite3

def test_underscore_simulators():
    """Test that simulators with underscores in their names work correctly"""
    print("Testing simulators with underscores in their names...\n")
    
    # Import our application
    sys.path.append('.')
    try:
        from db_viewer import DatabaseViewer
        import tkinter as tk
        print("✓ Application imports successfully")
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False
    
    # Get actual data from the database
    db_path = "/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db"
    
    if not os.path.exists(db_path):
        print(f"✗ Main database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get simulators with underscores
        cursor.execute("SELECT DISTINCT Simulator FROM Drives WHERE Simulator LIKE '%_%' ORDER BY Simulator")
        underscore_simulators = [row[0] for row in cursor.fetchall()]
        
        print(f"Found simulators with underscores: {underscore_simulators}")
        
        if not underscore_simulators:
            print("✗ No simulators with underscores found in database")
            conn.close()
            return False
        
        # Test each underscore simulator
        for simulator in underscore_simulators:
            print(f"\nTesting simulator: {simulator}")
            
            # Get a sample record for this simulator
            cursor.execute("SELECT * FROM Drives WHERE Simulator = ? LIMIT 1", (simulator,))
            sample_record = cursor.fetchone()
            
            if not sample_record:
                print(f"✗ No records found for simulator {simulator}")
                continue
            
            print(f"Sample record: {sample_record[:3]}...")  # Show first 3 fields
            
            # Check if the simulator directory exists
            sim_dir = f"/home/<USER>/Desktop/DriveDatabase/simulator_databases/{simulator}"
            if not os.path.exists(sim_dir):
                print(f"✗ Simulator directory not found: {sim_dir}")
                continue
            else:
                print(f"✓ Simulator directory exists: {sim_dir}")
            
            # Check what databases are in the directory
            import glob
            db_files = glob.glob(os.path.join(sim_dir, "*.db"))
            print(f"✓ Found {len(db_files)} database files in directory")
            
            # Test the application logic
            computer = str(sample_record[0]).replace(" ", "_")
            exact_match = os.path.join(sim_dir, f"{computer}.db")
            
            print(f"Looking for exact match: {exact_match}")
            if os.path.exists(exact_match):
                print(f"✓ Exact match found: {computer}.db")
            else:
                print(f"✗ Exact match not found, looking for partial matches...")
                partial_matches = glob.glob(os.path.join(sim_dir, f"*{computer}*.db"))
                if partial_matches:
                    print(f"✓ Partial matches found: {[os.path.basename(f) for f in partial_matches]}")
                else:
                    print(f"✗ No partial matches found for {computer}")
                    print(f"Available files: {[os.path.basename(f) for f in db_files]}")
        
        conn.close()
        
        # Test the application with a real underscore simulator record
        print(f"\nTesting application with real data...")
        
        root = tk.Tk()
        root.withdraw()
        
        app = DatabaseViewer(root)
        
        # Use the first underscore simulator we found
        test_simulator = underscore_simulators[0]
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM Drives WHERE Simulator = ? LIMIT 1", (test_simulator,))
        test_record = cursor.fetchone()
        conn.close()
        
        if test_record:
            print(f"Testing with record: Computer='{test_record[0]}', Simulator='{test_record[1]}'")
            
            try:
                app.show_record_details_in_panel(test_record)
                print("✓ Application successfully processed underscore simulator record")
                
                # Check if tabs were created
                children = app.right_panel.winfo_children()
                if len(children) > 0:
                    print("✓ Right panel populated with content")
                else:
                    print("✗ Right panel not populated")
                
            except Exception as e:
                print(f"✗ Error processing record: {e}")
                import traceback
                traceback.print_exc()
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("="*70)
    print("Testing Underscore Simulator Support")
    print("="*70)
    
    success = test_underscore_simulators()
    
    print("\n" + "="*70)
    if success:
        print("✅ UNDERSCORE SIMULATOR TESTS COMPLETED!")
        print("\nIf any issues were found above, they should now be identified.")
        print("The application should work correctly with simulators that have")
        print("underscores in their names (like 1678_1697 and 393_423).")
    else:
        print("❌ TESTS FAILED!")
        print("Please check the errors above.")
    print("="*70)

if __name__ == "__main__":
    main()
