#!/usr/bin/env python3
"""
Test script to verify the Log Comment column reordering works correctly
"""

import sqlite3
import os
import sys

def test_column_reordering():
    """Test that the column reordering functionality works"""
    print("Testing Log Comment column reordering...\n")
    
    # Import our application
    sys.path.append('.')
    try:
        from db_viewer import DatabaseViewer
        import tkinter as tk
        print("✓ Application imports successfully")
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False
    
    # Create test instance
    root = tk.Tk()
    root.withdraw()
    app = DatabaseViewer(root)
    
    # Test with actual database column structure
    db_path = "/home/<USER>/Desktop/DriveDatabase/simulator_databases/1477/Avionics_Software.db"
    
    if not os.path.exists(db_path):
        print(f"✗ Test database not found: {db_path}")
        root.destroy()
        return False
    
    try:
        # Get actual column structure from database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("PRAGMA table_info(logs)")
        column_info = cursor.fetchall()
        original_columns = [col[1] for col in column_info]
        
        cursor.execute("SELECT * FROM logs LIMIT 1")
        sample_row = cursor.fetchone()
        
        conn.close()
        
        print(f"Original database columns: {original_columns}")
        print(f"Sample row: {sample_row}")
        
        # Test column reordering
        reordered_columns = app.reorder_log_columns(original_columns)
        print(f"Reordered columns: {reordered_columns}")
        
        # Test row data reordering
        reordered_row = app.reorder_row_data(sample_row, original_columns, reordered_columns)
        print(f"Reordered row: {reordered_row}")
        
        # Verify Log Comment is in second position
        if len(reordered_columns) > 1 and reordered_columns[1] == "Log Comment":
            print("\n✓ Log Comment is correctly positioned as second column")
        else:
            print(f"\n✗ Log Comment is not in second position. Position: {reordered_columns.index('Log Comment') if 'Log Comment' in reordered_columns else 'Not found'}")
            root.destroy()
            return False
        
        # Verify data integrity
        if len(reordered_row) == len(original_columns):
            print("✓ Row data length matches column count")
        else:
            print(f"✗ Row data length mismatch: {len(reordered_row)} vs {len(original_columns)}")
            root.destroy()
            return False
        
        # Find Log Comment in original data
        log_comment_index = original_columns.index("Log Comment")
        original_log_comment = sample_row[log_comment_index]
        reordered_log_comment = reordered_row[1]  # Should be second position
        
        if original_log_comment == reordered_log_comment:
            print("✓ Log Comment data is correctly positioned")
            print(f"  Log Comment content: '{original_log_comment}'")
        else:
            print(f"✗ Log Comment data mismatch: '{original_log_comment}' vs '{reordered_log_comment}'")
            root.destroy()
            return False
        
        # Test with edge cases
        print("\nTesting edge cases...")
        
        # Test with columns that don't have Log Comment
        test_columns = ["Col1", "Col2", "Col3"]
        result = app.reorder_log_columns(test_columns)
        if result == test_columns:
            print("✓ Handles columns without Log Comment correctly")
        else:
            print("✗ Failed to handle columns without Log Comment")
            root.destroy()
            return False
        
        # Test with empty columns
        empty_result = app.reorder_log_columns([])
        if empty_result == []:
            print("✓ Handles empty column list correctly")
        else:
            print("✗ Failed to handle empty column list")
            root.destroy()
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        root.destroy()
        return False

def main():
    print("="*60)
    print("Testing Log Comment Column Reordering Functionality")
    print("="*60)
    
    success = test_column_reordering()
    
    print("\n" + "="*60)
    if success:
        print("✅ ALL TESTS PASSED!")
        print("\nThe Log Comment column will now appear as the second column")
        print("from the left in all related database tabs.")
        print("\nColumn order will be:")
        print("1. Letter/Prisec")
        print("2. Log Comment (wider column for better text display)")
        print("3. Asset Tag")
        print("4. Serial Number")
        print("5. Revision Number")
        print("6. Date")
        print("7. Action")
        print("\nYou can now run the application:")
        print("python3 db_viewer.py")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the errors above.")
    print("="*60)

if __name__ == "__main__":
    main()
