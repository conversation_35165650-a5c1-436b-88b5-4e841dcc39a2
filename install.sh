#!/bin/bash

# Installation script for Flyright Database Viewer

echo "Installing Flyright Database Viewer..."

# Make the Python script executable
chmod +x db_viewer.py

# Create local applications directory if it doesn't exist
mkdir -p ~/.local/share/applications

# Copy desktop entry to local applications
cp flyright-db-viewer.desktop ~/.local/share/applications/

# Update desktop database
if command -v update-desktop-database &> /dev/null; then
    update-desktop-database ~/.local/share/applications/
fi

echo "Installation complete!"
echo ""
echo "The application has been installed and should appear in your application menu."
echo "You can also run it directly with: python3 db_viewer.py"
echo ""
echo "To use with MenuLibre:"
echo "1. Open MenuLibre"
echo "2. Look for 'Flyright Database Viewer' in the Office or Development category"
echo "3. You can edit the menu entry properties as needed"
